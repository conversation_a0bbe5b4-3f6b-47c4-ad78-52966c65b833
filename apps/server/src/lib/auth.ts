import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import prisma from "../../prisma";

export const auth = betterAuth({
	database: prismaAdapter(prisma, {
		provider: "postgresql",
	}),
	trustedOrigins: [process.env.CORS_ORIGIN || ""],
	session: {
		// Session expires in 7 days
		expiresIn: 60 * 60 * 24 * 7, // 7 days
		// Update session expiration every 1 day when user is active
		updateAge: 60 * 60 * 24, // 1 day
		// Enable cookie caching for better performance
		cookieCache: {
			enabled: true,
			maxAge: 60 * 5, // 5 minutes cache duration
		},
		// Don't disable session refresh - we want automatic renewal
		disableSessionRefresh: false,
	},
	cookies: {
		sessionToken: {
			name: "better-auth.session_token",
			attributes: {
				httpOnly: true,
				secure: process.env.NODE_ENV === "production",
				sameSite: "lax",
				path: "/",
				// Set max age to match session expiration
				maxAge: 60 * 60 * 24 * 7, // 7 days
			},
		},
	},
	emailAndPassword: {
		enabled: true,
	},
});
