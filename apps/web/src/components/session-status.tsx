"use client";

import { Clock, RefreshCw, <PERSON>, ShieldAlert } from "lucide-react";
import { useSessionRefresh } from "@/hooks/use-session-refresh";
import { useSessionStatus } from "@/components/providers/session-provider";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "./ui/tooltip";

/**
 * Session Status Component
 *
 * Displays current session status and provides manual refresh capability.
 * Shows session expiration time and refresh status in the dashboard.
 */

interface SessionStatusProps {
	/** Show detailed session information (default: false) */
	showDetails?: boolean;
	/** Show manual refresh button (default: true) */
	showRefreshButton?: boolean;
	/** Compact display mode (default: false) */
	compact?: boolean;
	/** Custom className */
	className?: string;
}

export function SessionStatus({
	showDetails = false,
	showRefreshButton = true,
	compact = false,
	className = "",
}: SessionStatusProps) {
	const { sessionStatus } = useSessionStatus();
	const { refreshSession, isRefreshing } = useSessionRefresh();

	if (!sessionStatus) {
		return null;
	}

	const handleRefresh = async () => {
		await refreshSession();
	};

	const getStatusIcon = () => {
		if (sessionStatus.needsRefresh) {
			return <ShieldAlert className="h-4 w-4 text-amber-500" />;
		}
		return <Shield className="h-4 w-4 text-green-500" />;
	};

	const getStatusBadge = () => {
		if (sessionStatus.needsRefresh) {
			return (
				<Badge variant="secondary" className="gap-1">
					<ShieldAlert className="h-3 w-3" />
					Refreshing Soon
				</Badge>
			);
		}
		return (
			<Badge variant="default" className="gap-1">
				<Shield className="h-3 w-3" />
				Active
			</Badge>
		);
	};

	if (compact) {
		return (
			<TooltipProvider>
				<Tooltip>
					<TooltipTrigger asChild>
						<div className={`flex items-center gap-2 ${className}`}>
							{getStatusIcon()}
							{showRefreshButton && (
								<Button
									variant="ghost"
									size="sm"
									onClick={handleRefresh}
									disabled={isRefreshing}
									className="h-6 w-6 p-0"
								>
									<RefreshCw
										className={`h-3 w-3 ${isRefreshing ? "animate-spin" : ""}`}
									/>
								</Button>
							)}
						</div>
					</TooltipTrigger>
					<TooltipContent>
						<div className="space-y-1">
							<p className="font-medium">Session Status</p>
							<p className="text-sm">
								{sessionStatus.needsRefresh
									? "Session will refresh soon"
									: "Session is active"}
							</p>
							{sessionStatus.timeUntilExpiry && (
								<p className="text-xs text-muted-foreground">
									Expires in {sessionStatus.timeUntilExpiry}
								</p>
							)}
						</div>
					</TooltipContent>
				</Tooltip>
			</TooltipProvider>
		);
	}

	return (
		<div className={`flex items-center gap-3 ${className}`}>
			{getStatusBadge()}

			{showDetails && sessionStatus.timeUntilExpiry && (
				<div className="flex items-center gap-1 text-sm text-muted-foreground">
					<Clock className="h-3 w-3" />
					<span>Expires in {sessionStatus.timeUntilExpiry}</span>
				</div>
			)}

			{showRefreshButton && (
				<Button
					variant="outline"
					size="sm"
					onClick={handleRefresh}
					disabled={isRefreshing}
					className="gap-1"
				>
					<RefreshCw
						className={`h-3 w-3 ${isRefreshing ? "animate-spin" : ""}`}
					/>
					{isRefreshing ? "Refreshing..." : "Refresh"}
				</Button>
			)}
		</div>
	);
}

/**
 * Session Status Indicator
 *
 * Minimal session status indicator for headers or navigation
 */
export function SessionStatusIndicator({
	className = "",
}: {
	className?: string;
}) {
	return (
		<SessionStatus
			compact={true}
			showRefreshButton={false}
			className={className}
		/>
	);
}

/**
 * Session Status Panel
 *
 * Detailed session status panel for settings or admin areas
 */
export function SessionStatusPanel({ className = "" }: { className?: string }) {
	const { sessionStatus } = useSessionStatus();
	const { refreshSession, isRefreshing, sessionInfo } = useSessionRefresh();

	if (!sessionStatus) {
		return (
			<div className={`rounded-lg border p-4 ${className}`}>
				<p className="text-sm text-muted-foreground">
					Session information unavailable
				</p>
			</div>
		);
	}

	return (
		<div className={`rounded-lg border p-4 space-y-4 ${className}`}>
			<div className="flex items-center justify-between">
				<h3 className="font-medium">Session Status</h3>
				<SessionStatus compact={true} />
			</div>

			<div className="grid grid-cols-2 gap-4 text-sm">
				<div>
					<p className="text-muted-foreground">Status</p>
					<p className="font-medium">
						{sessionStatus.isValid ? "Active" : "Expired"}
					</p>
				</div>

				{sessionStatus.timeUntilExpiry && (
					<div>
						<p className="text-muted-foreground">Expires In</p>
						<p className="font-medium">{sessionStatus.timeUntilExpiry}</p>
					</div>
				)}

				{sessionStatus.expiresAt && (
					<div className="col-span-2">
						<p className="text-muted-foreground">Expires At</p>
						<p className="font-medium">
							{sessionStatus.expiresAt.toLocaleString()}
						</p>
					</div>
				)}
			</div>

			{sessionInfo?.needsRefresh && (
				<div className="rounded-md bg-amber-50 p-3 border border-amber-200">
					<div className="flex items-center gap-2">
						<ShieldAlert className="h-4 w-4 text-amber-600" />
						<p className="text-sm text-amber-800">
							Your session will be refreshed automatically soon.
						</p>
					</div>
				</div>
			)}

			<div className="flex justify-end">
				<Button
					variant="outline"
					onClick={() => refreshSession()}
					disabled={isRefreshing}
					className="gap-2"
				>
					<RefreshCw
						className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
					/>
					{isRefreshing ? "Refreshing Session..." : "Refresh Session"}
				</Button>
			</div>
		</div>
	);
}
