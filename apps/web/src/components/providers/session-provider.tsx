"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { authClient } from "@/lib/auth-client";
import {
	sessionRefreshService,
	type SessionInfo,
	type SessionRefreshConfig,
} from "@/lib/session-refresh";

/**
 * Session Management Provider
 *
 * Provides global session management context for the entire application.
 * Handles automatic session refresh, expiration detection, and user notifications.
 */

interface SessionContextValue {
	/** Current session information */
	sessionInfo: SessionInfo | null;
	/** Whether session monitoring is active */
	isMonitoring: boolean;
	/** Whether a refresh is in progress */
	isRefreshing: boolean;
	/** Manually refresh the session */
	refreshSession: () => Promise<boolean>;
	/** Start session monitoring */
	startMonitoring: () => void;
	/** Stop session monitoring */
	stopMonitoring: () => void;
	/** Update session refresh configuration */
	updateConfig: (config: Partial<SessionRefreshConfig>) => void;
}

const SessionContext = createContext<SessionContextValue | null>(null);

interface SessionProviderProps {
	children: React.ReactNode;
	/** Default session refresh configuration */
	config?: Partial<SessionRefreshConfig>;
	/** Whether to start monitoring automatically (default: true) */
	autoStart?: boolean;
	/** Redirect path on session expiry (default: "/login") */
	redirectOnExpiry?: string;
	/** Show toast notifications (default: true) */
	showNotifications?: boolean;
}

export function SessionProvider({
	children,
	config = {},
	autoStart = true,
	redirectOnExpiry = "/login",
	showNotifications = true,
}: SessionProviderProps) {
	const router = useRouter();
	const { data: session } = authClient.useSession();

	const [sessionInfo, setSessionInfo] = useState<SessionInfo | null>(null);
	const [isMonitoring, setIsMonitoring] = useState(false);
	const [isRefreshing, setIsRefreshing] = useState(false);

	// Configure session refresh service
	useEffect(() => {
		const serviceConfig: Partial<SessionRefreshConfig> = {
			checkInterval: 60 * 1000, // Check every minute
			refreshThreshold: 15 * 60 * 1000, // Refresh 15 minutes before expiry
			maxRetries: 3,
			...config,
			onSessionExpired: () => {
				if (showNotifications) {
					toast.error("Your session has expired. Please sign in again.", {
						duration: 5000,
					});
				}
				config.onSessionExpired?.();
				router.push(redirectOnExpiry);
			},
			onRefreshFailed: () => {
				if (showNotifications) {
					toast.error("Failed to refresh session. Please sign in again.", {
						duration: 5000,
					});
				}
				config.onRefreshFailed?.();
				router.push(redirectOnExpiry);
			},
			onRefreshSuccess: () => {
				if (showNotifications) {
					toast.success("Session refreshed successfully.", {
						duration: 2000,
					});
				}
				config.onRefreshSuccess?.();
			},
		};

		sessionRefreshService.updateConfig(serviceConfig);
	}, [config, router, redirectOnExpiry, showNotifications]);

	// Auto-start monitoring when session is available
	useEffect(() => {
		if (session && autoStart && !isMonitoring) {
			startMonitoring();
		} else if (!session && isMonitoring) {
			stopMonitoring();
		}
	}, [session, autoStart, isMonitoring]);

	// Update session info periodically
	useEffect(() => {
		if (!isMonitoring) {
			return;
		}

		const updateSessionInfo = async () => {
			try {
				const info = await sessionRefreshService.getSessionInfo();
				setSessionInfo(info);
			} catch (error) {
				console.error("Failed to update session info:", error);
			}
		};

		// Update immediately
		updateSessionInfo();

		// Update every 30 seconds
		const interval = setInterval(updateSessionInfo, 30 * 1000);

		return () => clearInterval(interval);
	}, [isMonitoring]);

	const startMonitoring = () => {
		if (!session) {
			return;
		}

		sessionRefreshService.start();
		setIsMonitoring(true);
	};

	const stopMonitoring = () => {
		sessionRefreshService.stop();
		setIsMonitoring(false);
		setSessionInfo(null);
	};

	const refreshSession = async (): Promise<boolean> => {
		setIsRefreshing(true);
		try {
			const result = await sessionRefreshService.refreshSession();
			return result;
		} finally {
			setIsRefreshing(false);
		}
	};

	const updateConfig = (newConfig: Partial<SessionRefreshConfig>) => {
		sessionRefreshService.updateConfig(newConfig);
	};

	const contextValue: SessionContextValue = {
		sessionInfo,
		isMonitoring,
		isRefreshing,
		refreshSession,
		startMonitoring,
		stopMonitoring,
		updateConfig,
	};

	return (
		<SessionContext.Provider value={contextValue}>
			{children}
		</SessionContext.Provider>
	);
}

/**
 * Hook to access session management context
 */
export function useSessionContext(): SessionContextValue {
	const context = useContext(SessionContext);
	if (!context) {
		throw new Error("useSessionContext must be used within a SessionProvider");
	}
	return context;
}

/**
 * Hook for session-aware API calls with automatic retry
 */
export function useSessionAwareApi() {
	const { refreshSession } = useSessionContext();

	const apiCall = async <T,>(
		apiFunction: () => Promise<T>,
		maxRetries = 1,
	): Promise<T> => {
		let retries = 0;

		while (retries <= maxRetries) {
			try {
				return await apiFunction();
			} catch (error) {
				// Check if it's a 401 error
				if (
					error &&
					typeof error === "object" &&
					"status" in error &&
					error.status === 401 &&
					retries < maxRetries
				) {
					// Try to refresh session
					const refreshed = await refreshSession();
					if (refreshed) {
						retries++;
						continue;
					}
				}
				throw error;
			}
		}

		throw new Error("Max retries exceeded");
	};

	return { apiCall };
}

/**
 * Session Status Hook
 *
 * Provides formatted session status information
 */
export function useSessionStatus() {
	const { sessionInfo } = useSessionContext();

	const formatTimeUntilExpiry = (milliseconds: number): string => {
		const minutes = Math.floor(milliseconds / (1000 * 60));
		const hours = Math.floor(minutes / 60);
		const days = Math.floor(hours / 24);

		if (days > 0) {
			return `${days} day${days > 1 ? "s" : ""}`;
		}
		if (hours > 0) {
			return `${hours} hour${hours > 1 ? "s" : ""}`;
		}
		return `${minutes} minute${minutes > 1 ? "s" : ""}`;
	};

	const sessionStatus = sessionInfo
		? {
				isValid: sessionInfo.isValid,
				needsRefresh: sessionInfo.needsRefresh,
				timeUntilExpiry: sessionInfo.timeUntilExpiry
					? formatTimeUntilExpiry(sessionInfo.timeUntilExpiry)
					: null,
				expiresAt: sessionInfo.expiresAt,
			}
		: null;

	return { sessionStatus, sessionInfo };
}
