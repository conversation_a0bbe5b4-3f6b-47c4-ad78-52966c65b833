/**
 * User Management Table Component
 * Displays and manages users with role-based actions and status controls
 */

"use client";

import type { UserRole, User as UserType } from "@snapback/shared";
import { format } from "date-fns";
import {
	AlertTriangle,
	Crown,
	Edit,
	RefreshCw,
	Shield,
	ShieldCheck,
	User,
	UserCheck,
	Users,
	UserX,
} from "lucide-react";
import Image from "next/image";
import { useState } from "react";
// Import new reusable dashboard components
import {
	DashboardHeader,
	DataTable,
	EnhancedPagination,
	Filters,
} from "@/components/dashboard";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useDashboardState } from "@/hooks/use-url-state";
import {
	useToggleUserStatus,
	useUserPermissions,
	useUsers,
} from "@/hooks/use-user-management";
import type {
	DashboardHeaderAction,
	FilterConfig,
	TableColumn,
	TableRowAction,
	TableSortConfig,
} from "@/types/dashboard-components";

interface UsersTableProps {
	className?: string;
}

const ROLE_CONFIG: Record<
	UserRole,
	{
		label: string;
		variant: "default" | "secondary" | "destructive" | "outline";
		icon: React.ComponentType<{ className?: string }>;
	}
> = {
	SUPER_ADMIN: {
		label: "Super Admin",
		variant: "destructive",
		icon: Crown,
	},
	ADMIN: {
		label: "Admin",
		variant: "default",
		icon: ShieldCheck,
	},
	MANAGER: {
		label: "Manager",
		variant: "secondary",
		icon: Shield,
	},
	USER: {
		label: "User",
		variant: "outline",
		icon: User,
	},
	VIEWER: {
		label: "Viewer",
		variant: "outline",
		icon: UserCheck,
	},
};

const STATUS_CONFIG = {
	active: {
		label: "Active",
		variant: "default" as const,
		icon: UserCheck,
	},
	inactive: {
		label: "Inactive",
		variant: "secondary" as const,
		icon: UserX,
	},
};

export function UsersTable({ className }: UsersTableProps) {
	const [toggleUserId, setToggleUserId] = useState<string | null>(null);
	const [toggleAction, setToggleAction] = useState<
		"activate" | "deactivate" | null
	>(null);

	const { canManageUsers, canDeactivateUsers } = useUserPermissions();

	// Use URL state management for filters and pagination
	const {
		filters,
		updateFilter,
		page,
		limit,
		updatePage,
		updateLimit,
		sort,
		order,
		updateSort,
		resetAll,
	} = useDashboardState(
		{
			search: "",
			role: "",
			status: "",
		},
		1, // default page
		20, // default limit
	);

	const {
		data: usersData,
		error,
		refetch,
	} = useUsers({
		page,
		search: filters.search || undefined,
		role: filters.role === "" ? undefined : (filters.role as UserRole),
		isActive: filters.status === "" ? undefined : filters.status === "active",
		limit,
	});

	const toggleUserStatus = useToggleUserStatus();

	const handleToggleUserStatus = async () => {
		if (!toggleUserId || !toggleAction) return;

		try {
			await toggleUserStatus.mutateAsync({
				userId: toggleUserId,
				isActive: toggleAction === "activate",
			});
			setToggleUserId(null);
			setToggleAction(null);
		} catch (error) {
			// Error handling is done in the mutation hook
			console.error("Failed to toggle user status:", error);
		}
	};

	// Don't render if user doesn't have permission
	if (!canManageUsers) {
		return null;
	}

	// Apply sorting to users data
	const sortedUsers = usersData?.users
		? [...usersData.users].sort((a, b) => {
				if (!sort) return 0;

				const aValue = a[sort as keyof UserType];
				const bValue = b[sort as keyof UserType];

				if (typeof aValue === "string" && typeof bValue === "string") {
					return order === "asc"
						? aValue.toLowerCase().localeCompare(bValue.toLowerCase())
						: bValue.toLowerCase().localeCompare(aValue.toLowerCase());
				}

				if (
					aValue &&
					bValue &&
					typeof aValue === "object" &&
					typeof bValue === "object" &&
					"getTime" in aValue &&
					"getTime" in bValue
				) {
					const aTime = (aValue as Date).getTime();
					const bTime = (bValue as Date).getTime();
					return order === "asc" ? aTime - bTime : bTime - aTime;
				}

				return 0;
			})
		: [];

	// Apply pagination
	const startIndex = (page - 1) * limit;
	const endIndex = startIndex + limit;
	const paginatedUsers = sortedUsers.slice(startIndex, endIndex);

	// Create pagination meta
	const paginationMeta = {
		page,
		limit,
		totalCount: sortedUsers.length,
		totalPages: Math.ceil(sortedUsers.length / limit),
		hasNextPage: endIndex < sortedUsers.length,
		hasPreviousPage: page > 1,
	};

	// Dashboard Header Configuration
	const headerActions: DashboardHeaderAction[] = [
		{
			label: "Refresh",
			icon: RefreshCw,
			onClick: () => refetch(),
			variant: "outline",
		},
	];

	// Filter Configuration
	const filterConfigs: FilterConfig[] = [
		{
			type: "search",
			key: "search",
			placeholder: "Search users by name or email...",
			icon: User,
		},
		{
			type: "select",
			key: "role",
			label: "Role",
			placeholder: "All Roles",
			icon: Shield,
			options: [
				{ label: "Super Admin", value: "SUPER_ADMIN" },
				{ label: "Admin", value: "ADMIN" },
				{ label: "Manager", value: "MANAGER" },
				{ label: "User", value: "USER" },
				{ label: "Viewer", value: "VIEWER" },
			],
		},
		{
			type: "select",
			key: "status",
			label: "Status",
			placeholder: "All Status",
			icon: UserCheck,
			options: [
				{ label: "Active", value: "active" },
				{ label: "Inactive", value: "inactive" },
			],
		},
	];

	// Table Columns Configuration
	const columns: TableColumn<UserType>[] = [
		{
			key: "name",
			header: "User",
			sortable: true,
			render: (_, user) => (
				<div className="flex items-center gap-3">
					<div className="flex-shrink-0">
						{user.image ? (
							<Image
								src={user.image}
								alt={user.name}
								width={32}
								height={32}
								className="h-8 w-8 rounded-full"
							/>
						) : (
							<div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
								<User className="h-4 w-4" />
							</div>
						)}
					</div>
					<div>
						<div className="font-medium">{user.name}</div>
						<div className="text-muted-foreground text-sm">{user.email}</div>
					</div>
				</div>
			),
			mobileRender: (user) => (
				<div className="flex items-center gap-3">
					<div className="flex-shrink-0">
						{user.image ? (
							<Image
								src={user.image}
								alt={user.name}
								width={32}
								height={32}
								className="h-8 w-8 rounded-full"
							/>
						) : (
							<div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
								<User className="h-4 w-4" />
							</div>
						)}
					</div>
					<div>
						<div className="font-medium">{user.name}</div>
						<div className="text-muted-foreground text-sm">{user.email}</div>
					</div>
				</div>
			),
		},
		{
			key: "role",
			header: "Role",
			sortable: true,
			render: (_, user) => {
				const roleConfig = ROLE_CONFIG[user.role as UserRole];
				const RoleIcon = roleConfig.icon;
				return (
					<Badge variant={roleConfig.variant} className="gap-1">
						<RoleIcon className="h-3 w-3" />
						{roleConfig.label}
					</Badge>
				);
			},
		},
		{
			key: "isActive",
			header: "Status",
			render: (_, user) => {
				const statusConfig =
					STATUS_CONFIG[user.isActive ? "active" : "inactive"];
				const StatusIcon = statusConfig.icon;
				return (
					<Badge variant={statusConfig.variant} className="gap-1">
						<StatusIcon className="h-3 w-3" />
						{statusConfig.label}
					</Badge>
				);
			},
		},
		{
			key: "createdAt",
			header: "Joined",
			sortable: true,
			render: (value) => format(new Date(value as string), "MMM d, yyyy"),
		},
		{
			key: "lastLoginAt",
			header: "Last Login",
			sortable: true,
			render: (value) =>
				value ? format(new Date(value as string), "MMM d, yyyy") : "Never",
		},
	];

	// Table Row Actions Configuration
	const rowActions: TableRowAction<UserType>[] = [
		{
			label: "Edit User",
			icon: Edit,
			onClick: (user) => {
				// TODO: Implement edit functionality
				console.log("Edit user:", user.id);
			},
		},
		...(canDeactivateUsers
			? [
					{
						label: "Toggle Status",
						icon: UserX,
						onClick: (user) => {
							setToggleUserId(user.id);
							setToggleAction(user.isActive ? "deactivate" : "activate");
						},
						separator: true,
					} as TableRowAction<UserType>,
				]
			: []),
	];

	// Handle sort changes
	const handleSort = (sortConfig: TableSortConfig) => {
		updateSort(sortConfig.key, sortConfig.direction);
	};

	// Handle filter changes
	const handleFilterChange = (
		key: string,
		value: string | string[] | boolean | undefined,
	) => {
		updateFilter(key as keyof typeof filters, value as string);
	};

	// Handle filter clear
	const handleFilterClear = () => {
		resetAll();
	};

	if (error) {
		return (
			<Card className={className}>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Users className="h-5 w-5" />
						User Management
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="py-8 text-center">
						<AlertTriangle className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
						<p className="mb-4 text-muted-foreground">Failed to load users</p>
						<Button onClick={() => refetch()} variant="outline">
							<RefreshCw className="mr-2 h-4 w-4" />
							Try Again
						</Button>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<div className={`space-y-6 ${className}`}>
			{/* Dashboard Header */}
			<DashboardHeader
				title="User Management"
				subtitle="Manage user accounts, roles, and permissions"
				actions={[
					...headerActions,
					{
						label: "Invite User",
						icon: Users,
						onClick: () => {
							// This will be handled by the InvitationForm component
						},
						variant: "default" as const,
					},
				]}
			/>

			{/* Filters */}
			<Filters
				filters={filterConfigs}
				values={filters}
				onChange={handleFilterChange}
				onClear={handleFilterClear}
			/>

			{/* Data Table */}
			<DataTable
				data={paginatedUsers}
				columns={columns}
				actions={rowActions}
				sortConfig={sort ? { key: sort, direction: order } : undefined}
				onSort={handleSort}
				emptyMessage="No users found"
				emptyIcon={Users}
			/>

			{/* Pagination */}
			<EnhancedPagination
				meta={paginationMeta}
				page={page}
				limit={limit}
				onPageChange={updatePage}
				onPageSizeChange={updateLimit}
				showInfo={true}
			/>

			{/* Status Toggle Confirmation Dialog */}
			<AlertDialog
				open={!!toggleUserId}
				onOpenChange={() => {
					setToggleUserId(null);
					setToggleAction(null);
				}}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>
							{toggleAction === "activate" ? "Activate" : "Deactivate"} User
						</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to{" "}
							{toggleAction === "activate" ? "activate" : "deactivate"} this
							user?
							{toggleAction === "deactivate" &&
								" They will lose access to the system immediately."}
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction onClick={handleToggleUserStatus}>
							{toggleAction === "activate" ? "Activate" : "Deactivate"}
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</div>
	);
}
