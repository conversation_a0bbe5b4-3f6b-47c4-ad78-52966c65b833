import type {
	AnalyticsData,
	APIError,
	CheckoutSessionDetails,
	CreateCheckoutSessionRequest,
	CreateCheckoutSessionResponse,
	CreateLicenseRequest,
	CreatePaymentIntentRequest,
	CreatePaymentIntentResponse,
	CreateUpgradePaymentIntentRequest,
	CreateUserRequest,
	DashboardStats,
	License,
	LicenseStatusResponse,
	LicenseValidationResponse,
	ListLicensesRequest,
	ListLicensesResponse,
	ListUsersRequest,
	ListUsersResponse,
	PricingInfo,
	ProcessRefundRequest,
	RecentActivity,
	RefundHistoryResponse,
	RefundStatusResponse,
	RequestRefundRequest,
	SendInvitationRequest,
	SendInvitationResponse,
	UpdateDeviceMetadataRequest,
	UpdateUserRequest,
	UpgradeLicenseRequest,
	User,
	UserInvitation,
	UserPermissions,
	ValidateLicenseRequest,
} from "@snapback/shared";

const API_BASE_URL =
	process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000/api";

class ApiError extends Error {
	constructor(
		message: string,
		public status: number,
		public code?: string,
		public details?: Record<string, unknown>,
	) {
		super(message);
		this.name = "ApiError";
	}
}

async function apiRequest<T>(
	endpoint: string,
	options: RequestInit = {},
	retryCount = 0,
): Promise<T> {
	const url = `${API_BASE_URL}${endpoint}`;

	const config: RequestInit = {
		headers: {
			"Content-Type": "application/json",
			...options.headers,
		},
		credentials: "include", // Include cookies for authentication
		...options,
	};

	try {
		const response = await fetch(url, config);

		if (!response.ok) {
			const errorData: APIError = await response.json().catch(() => ({
				error: "Unknown error occurred",
				code: "UNKNOWN_ERROR",
			}));

			// Handle 401 errors with session refresh
			if (response.status === 401 && retryCount === 0) {
				try {
					// Import authClient dynamically to avoid circular dependencies
					const { authClient } = await import("./auth-client");

					// Try to refresh the session
					const session = await authClient.getSession();

					if (session) {
						// Session is still valid, retry the request
						return apiRequest<T>(endpoint, options, retryCount + 1);
					}
				} catch (refreshError) {
					console.error("Session refresh failed:", refreshError);
					// Fall through to throw the original 401 error
				}
			}

			throw new ApiError(
				errorData.error || "Request failed",
				response.status,
				errorData.code,
				errorData.details,
			);
		}

		return await response.json();
	} catch (error) {
		if (error instanceof ApiError) {
			throw error;
		}

		throw new ApiError("Network error occurred", 0, "NETWORK_ERROR");
	}
}

// License Management API
export const licenseApi = {
	// List licenses with pagination and filtering
	async list(params?: ListLicensesRequest): Promise<ListLicensesResponse> {
		const searchParams = new URLSearchParams();
		if (params?.page) searchParams.set("page", params.page.toString());
		if (params?.limit) searchParams.set("limit", params.limit.toString());
		if (params?.licenseType)
			searchParams.set("licenseType", params.licenseType);
		if (params?.isActive !== undefined)
			searchParams.set("isActive", params.isActive.toString());
		if (params?.isExpired !== undefined)
			searchParams.set("isExpired", params.isExpired.toString());
		if (params?.search) searchParams.set("search", params.search);
		if (params?.email) searchParams.set("email", params.email);

		const query = searchParams.toString();
		return apiRequest(`/licenses${query ? `?${query}` : ""}`);
	},

	async create(data: CreateLicenseRequest): Promise<License> {
		return apiRequest<License>("/licenses/create", {
			method: "POST",
			body: JSON.stringify(data),
		});
	},

	async validate(
		data: ValidateLicenseRequest,
	): Promise<LicenseValidationResponse> {
		return apiRequest<LicenseValidationResponse>("/licenses/validate", {
			method: "POST",
			body: JSON.stringify(data),
		});
	},

	async getStatus(licenseKey: string): Promise<LicenseStatusResponse> {
		return apiRequest<LicenseStatusResponse>(`/licenses/status/${licenseKey}`);
	},

	async upgrade(data: UpgradeLicenseRequest): Promise<License> {
		return apiRequest<License>("/licenses/upgrade", {
			method: "POST",
			body: JSON.stringify(data),
		});
	},

	async resend(email: string): Promise<{ message: string }> {
		return apiRequest("/licenses/resend", {
			method: "POST",
			body: JSON.stringify({ email }),
		});
	},

	async removeDevice(
		deviceId: string,
		token: string,
	): Promise<{ message: string }> {
		return apiRequest(`/licenses/devices/${deviceId}`, {
			method: "DELETE",
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});
	},

	async updateDeviceMetadata(
		data: UpdateDeviceMetadataRequest,
		token: string,
	): Promise<{ success: boolean; message: string }> {
		return apiRequest<{ success: boolean; message: string }>(
			"/licenses/devices/metadata",
			{
				method: "PUT",
				body: JSON.stringify(data),
				headers: {
					Authorization: `Bearer ${token}`,
				},
			},
		);
	},
};

// Payment Processing API
export const paymentApi = {
	async getPricing(): Promise<PricingInfo> {
		return apiRequest<PricingInfo>("/payments/pricing");
	},

	async createPaymentIntent(
		data: CreatePaymentIntentRequest,
	): Promise<CreatePaymentIntentResponse> {
		return apiRequest<CreatePaymentIntentResponse>(
			"/payments/create-payment-intent",
			{
				method: "POST",
				body: JSON.stringify(data),
			},
		);
	},

	async createCheckoutSession(
		data: CreateCheckoutSessionRequest,
	): Promise<CreateCheckoutSessionResponse> {
		return apiRequest<CreateCheckoutSessionResponse>(
			"/payments/create-checkout-session",
			{
				method: "POST",
				body: JSON.stringify(data),
			},
		);
	},

	async createUpgradePaymentIntent(
		data: CreateUpgradePaymentIntentRequest,
	): Promise<CreatePaymentIntentResponse> {
		return apiRequest<CreatePaymentIntentResponse>(
			"/payments/create-upgrade-payment-intent",
			{
				method: "POST",
				body: JSON.stringify(data),
			},
		);
	},

	async getCheckoutSessionStatus(
		sessionId: string,
	): Promise<CheckoutSessionDetails> {
		return apiRequest<CheckoutSessionDetails>(
			`/payments/checkout-session/${sessionId}`,
		);
	},

	async getPaymentIntentStatus(
		paymentIntentId: string,
	): Promise<{ status: string; amount: number; currency: string }> {
		return apiRequest<{ status: string; amount: number; currency: string }>(
			`/payments/payment-intent/${paymentIntentId}`,
		);
	},
};

// Refund Management API
export const refundApi = {
	async request(
		data: RequestRefundRequest,
	): Promise<{ success: boolean; message: string; refundRequestId?: string }> {
		return apiRequest<{
			success: boolean;
			message: string;
			refundRequestId?: string;
		}>("/refunds/request", {
			method: "POST",
			body: JSON.stringify(data),
		});
	},

	async process(
		data: ProcessRefundRequest,
	): Promise<{ success: boolean; message: string; refundId?: string }> {
		return apiRequest<{ success: boolean; message: string; refundId?: string }>(
			"/refunds/process",
			{
				method: "POST",
				body: JSON.stringify(data),
			},
		);
	},

	async getStatus(licenseKey: string): Promise<RefundStatusResponse> {
		return apiRequest<RefundStatusResponse>(`/refunds/status/${licenseKey}`);
	},

	async getHistory(params?: {
		page?: number;
		limit?: number;
		status?: string;
	}): Promise<RefundHistoryResponse> {
		const searchParams = new URLSearchParams();
		if (params?.page) searchParams.set("page", params.page.toString());
		if (params?.limit) searchParams.set("limit", params.limit.toString());
		if (params?.status) searchParams.set("status", params.status);

		const query = searchParams.toString();
		return apiRequest<RefundHistoryResponse>(
			`/refunds/history${query ? `?${query}` : ""}`,
		);
	},
};

// Dashboard Analytics API (these would need to be implemented on the server)
export const analyticsApi = {
	async getDashboardStats(): Promise<DashboardStats> {
		return apiRequest<DashboardStats>("/analytics/dashboard-stats");
	},

	async getAnalyticsData(timeRange = "30d"): Promise<AnalyticsData> {
		return apiRequest<AnalyticsData>(`/analytics/data?timeRange=${timeRange}`);
	},

	async getRecentActivity(limit = 10): Promise<RecentActivity[]> {
		return apiRequest<RecentActivity[]>(
			`/analytics/recent-activity?limit=${limit}`,
		);
	},

	async getLicenses(params?: {
		page?: number;
		limit?: number;
		type?: string;
	}): Promise<{
		licenses: License[];
		pagination: {
			page: number;
			limit: number;
			totalCount: number;
			totalPages: number;
			hasNextPage: boolean;
			hasPreviousPage: boolean;
		};
	}> {
		const searchParams = new URLSearchParams();
		if (params?.page) searchParams.set("page", params.page.toString());
		if (params?.limit) searchParams.set("limit", params.limit.toString());
		if (params?.type) searchParams.set("type", params.type);

		const query = searchParams.toString();
		return apiRequest(`/analytics/licenses${query ? `?${query}` : ""}`);
	},

	async getCustomers(params?: { page?: number; limit?: number }): Promise<{
		customers: Array<{
			id: string;
			email: string;
			totalLicenses: number;
			totalSpent: number;
			createdAt: string;
		}>;
		pagination: {
			page: number;
			limit: number;
			totalCount: number;
			totalPages: number;
			hasNextPage: boolean;
			hasPreviousPage: boolean;
		};
	}> {
		const searchParams = new URLSearchParams();
		if (params?.page) searchParams.set("page", params.page.toString());
		if (params?.limit) searchParams.set("limit", params.limit.toString());

		const query = searchParams.toString();
		return apiRequest(`/analytics/customers${query ? `?${query}` : ""}`);
	},
}; // User management API
export const usersApi = {
	// Get current user info and permissions
	getCurrentUser: (): Promise<{
		user: User;
		permissions: UserPermissions;
		trialInfo?: {
			hasTrialLicense: boolean;
			shouldShowUpgradePrompts: boolean;
			trialStatus?: {
				success: boolean;
				isTrialLicense: boolean;
				isExpired?: boolean;
				daysRemaining?: number;
				expiresAt?: Date;
			};
		};
	}> => {
		return apiRequest("/users/me");
	},

	// List users with pagination and filtering
	listUsers: (params?: ListUsersRequest): Promise<ListUsersResponse> => {
		const searchParams = new URLSearchParams();
		if (params?.page) searchParams.set("page", params.page.toString());
		if (params?.limit) searchParams.set("limit", params.limit.toString());
		if (params?.role) searchParams.set("role", params.role);
		if (params?.isActive !== undefined)
			searchParams.set("isActive", params.isActive.toString());
		if (params?.search) searchParams.set("search", params.search);

		const query = searchParams.toString();
		return apiRequest(`/users${query ? `?${query}` : ""}`);
	},

	// Create a new user
	createUser: (
		userData: CreateUserRequest,
	): Promise<{
		user: User;
		message: string;
	}> => {
		return apiRequest("/users", {
			method: "POST",
			body: JSON.stringify(userData),
		});
	},

	// Update user information
	updateUser: (
		userId: string,
		updateData: UpdateUserRequest,
	): Promise<{
		user: User;
		message: string;
	}> => {
		return apiRequest(`/users/${userId}`, {
			method: "PATCH",
			body: JSON.stringify(updateData),
		});
	},

	// Toggle user active status
	toggleUserStatus: (
		userId: string,
		isActive: boolean,
	): Promise<{
		user: User;
		message: string;
	}> => {
		return apiRequest(`/users/${userId}/status`, {
			method: "PATCH",
			body: JSON.stringify({ isActive }),
		});
	},

	// Send user invitation
	sendInvitation: (
		invitationData: SendInvitationRequest,
	): Promise<SendInvitationResponse> => {
		return apiRequest("/users/invite", {
			method: "POST",
			body: JSON.stringify(invitationData),
		});
	},

	// Revoke user invitation
	revokeInvitation: (
		invitationId: string,
	): Promise<{
		message: string;
	}> => {
		return apiRequest(`/users/invite/${invitationId}`, {
			method: "DELETE",
		});
	},

	// List pending invitations
	listInvitations: (params?: {
		page?: number;
		limit?: number;
		status?: string;
	}): Promise<{
		invitations: UserInvitation[];
		pagination: {
			page: number;
			limit: number;
			totalCount: number;
			totalPages: number;
			hasNextPage: boolean;
			hasPreviousPage: boolean;
		};
	}> => {
		const searchParams = new URLSearchParams();
		if (params?.page) searchParams.set("page", params.page.toString());
		if (params?.limit) searchParams.set("limit", params.limit.toString());
		if (params?.status) searchParams.set("status", params.status);

		const query = searchParams.toString();
		return apiRequest(`/users/invitations${query ? `?${query}` : ""}`);
	},
};

export { ApiError };
